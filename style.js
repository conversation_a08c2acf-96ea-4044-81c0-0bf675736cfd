document.addEventListener("DOMContentLoaded", function () {
  // Slider functionality
  const slides = document.querySelectorAll(".slide");
  const navDots = document.querySelectorAll(".nav-dot");
  let currentSlide = 0;
  let slideInterval;
  const slideIntervalTime = 5000; // 5 seconds

  // Initialize slider only if elements exist
  if (slides.length > 0 && navDots.length > 0) {
    // Set background images
    slides.forEach((slide) => {
      const bgImage = slide.getAttribute("data-bg");
      if (bgImage) {
        slide.style.backgroundImage = `url('${bgImage}')`;
      }
    });

    // Function to show specific slide
    function showSlide(index) {
      if (index < 0 || index >= slides.length) return;

      // Remove active class from all slides and dots
      slides.forEach((slide) => slide.classList.remove("active"));
      navDots.forEach((dot) => dot.classList.remove("active"));

      // Add active class to current slide and dot
      slides[index].classList.add("active");
      if (navDots[index]) {
        navDots[index].classList.add("active");
      }

      currentSlide = index;
    }

    // Function to go to next slide
    function nextSlide() {
      const next = (currentSlide + 1) % slides.length;
      showSlide(next);
    }

    // Function to start auto-advance
    function startSlideInterval() {
      slideInterval = setInterval(nextSlide, slideIntervalTime);
    }

    // Function to stop auto-advance
    function stopSlideInterval() {
      if (slideInterval) {
        clearInterval(slideInterval);
      }
    }

    // Add click event listeners to navigation dots
    navDots.forEach((dot, index) => {
      dot.addEventListener("click", () => {
        stopSlideInterval();
        showSlide(index);
        startSlideInterval();
      });
    });

    // Pause on hover
    const sliderContainer = document.querySelector(".hero-slider");
    if (sliderContainer) {
      sliderContainer.addEventListener("mouseenter", stopSlideInterval);
      sliderContainer.addEventListener("mouseleave", startSlideInterval);
    }

    // Initialize first slide and start auto-advance
    showSlide(0);
    startSlideInterval();
  }

  // Smooth scrolling for navigation links
  const navLinks = document.querySelectorAll(".menu__link");
  navLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      const href = this.getAttribute("href");
      if (href && href.startsWith("#")) {
        e.preventDefault();
        const target = document.querySelector(href);
        if (target) {
          target.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }
    });
  });

  // Newsletter form handling
  const newsletterForm = document.querySelector(".newsletter-form");
  if (newsletterForm) {
    newsletterForm.addEventListener("submit", function (e) {
      e.preventDefault();
      const emailInput = this.querySelector(".newsletter-input");
      const email = emailInput.value.trim();

      if (email && isValidEmail(email)) {
        // Here you would typically send the email to your server
        alert("Thank you for subscribing to our newsletter!");
        emailInput.value = "";
      } else {
        alert("Please enter a valid email address.");
      }
    });
  }

  // Email validation function
  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  let acc = document.getElementsByClassName('accordion')[0]
  let panelElm = document.getElementsByClassName('panel')[0]

function accHandler (){
  if(acc.classList.contains('active')){
    acc.classList.remove('active')
    acc.innerHTML = '+'
    panelElm.classList.remove('open')
    return
  }else{
    acc.classList.add('active')
    acc.innerHTML = '-'
    panelElm.classList.add('open')
    return
  }

}

  acc.addEventListener('click',accHandler)

  // let acc = document.getElementsByClassName("accordion");
  // let i;

  // for (i = 0; i < acc.length; i++) {
  //   acc[i].addEventListener("click", function () {
  //     /* Toggle between adding and removing the "active" class,
  //   to highlight the button that controls the panel */
  //     this.classList.toggle("active");

  //     /* Toggle between hiding and showing the active panel */
  //     let panel = this.nextElementSibling;
  //     if (panel.style.display === "block") {
  //       panel.style.display = "none";
  //     } else {
  //       panel.style.display = "block";
  //     }
  //   });
  // }
});
